# 台账数据提取工具

这是一个自动化脚本，用于从exe软件中提取台账数据并保存为Excel文件。

## 功能特点

- 🖥️ **图形化界面** - 简洁易用的GUI界面
- ⌨️ **自动键盘控制** - 自动按下↓键和Ctrl+C进行数据提取
- 📋 **智能剪贴板监控** - 检测数据变化，自动停止提取
- 📊 **Excel导出** - 将提取的数据保存为xlsx格式
- ⚙️ **可配置参数** - 支持自定义操作间隔、重试次数等
- 📝 **实时日志** - 显示操作进度和状态信息
- 💾 **配置保存** - 自动保存用户设置

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 启动程序
```bash
python data_extractor.py
```

### 2. 配置参数
- **操作间隔时间**: 设置每次操作之间的等待时间（建议0.5-2秒）
- **最大无变化尝试次数**: 当数据不再变化时，尝试的最大次数（建议3-5次）
- **输出路径**: 选择Excel文件的保存位置

### 3. 准备目标软件
- 打开包含台账数据的exe软件
- 将光标定位到数据的起始位置
- 确保软件窗口处于活动状态

### 4. 开始提取
- 点击"开始提取"按钮
- 程序会倒计时3秒后开始自动操作
- 实时查看提取进度和日志信息

### 5. 保存数据
- 提取完成后，点击"保存数据"按钮
- 数据将保存为Excel文件，文件名包含时间戳

## 工作原理

1. **按下↓键** - 移动到下一行数据
2. **按下Ctrl+C** - 复制当前行数据到剪贴板
3. **检查数据变化** - 比较当前数据与上一次数据
4. **重复执行** - 如果数据有变化，继续步骤1-3
5. **自动停止** - 当连续几次数据无变化时自动停止
6. **保存数据** - 将所有提取的数据保存到Excel文件

## 注意事项

### 使用前准备
- 确保目标软件支持键盘导航（↓键）
- 确保目标软件支持复制操作（Ctrl+C）
- 建议先手动测试几次操作确认可行性

### 安全提示
- 程序运行时请勿操作鼠标和键盘
- 如需紧急停止，可将鼠标移动到屏幕左上角
- 建议在测试环境中先试运行

### 性能优化
- 根据软件响应速度调整操作间隔时间
- 对于大量数据，建议适当增加间隔时间
- 定期清理输出目录中的旧文件

## 配置文件

程序会自动创建`config.json`文件保存用户设置：

```json
{
  "delay_between_actions": 0.5,
  "max_unchanged_attempts": 3,
  "output_path": "C:\\Users\\<USER>\\Documents"
}
```

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本（建议3.7+）
   - 确认所有依赖包已正确安装

2. **键盘操作无效**
   - 确认目标软件窗口处于活动状态
   - 检查软件是否支持键盘快捷键
   - 尝试增加操作间隔时间

3. **数据提取不完整**
   - 增加"最大无变化尝试次数"
   - 检查目标软件的数据加载速度
   - 适当增加操作间隔时间

4. **Excel文件保存失败**
   - 检查输出路径是否存在且有写入权限
   - 确认文件名不包含非法字符
   - 检查磁盘空间是否充足

### 日志分析
程序会在界面中显示详细的操作日志，包括：
- 操作时间戳
- 执行的具体操作
- 数据提取状态
- 错误信息（如有）

## 技术支持

如遇到问题，请检查：
1. 操作日志中的错误信息
2. 目标软件的兼容性
3. 系统权限设置
4. 依赖包版本

## 版本历史

- v1.0.0 - 初始版本，支持基本的数据提取功能
