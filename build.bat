@echo off
chcp 65001 >nul
title 台账数据提取工具 - 打包脚本

echo ============================================================
echo                台账数据提取工具 - 打包脚本
echo ============================================================
echo.

REM 检查Python是否安装
echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检测成功

REM 检查主文件
if not exist "data_extractor.py" (
    echo ❌ 找不到主文件 data_extractor.py
    echo 请确保在正确的目录中运行此脚本
    echo.
    pause
    exit /b 1
)

echo ✅ 主文件检查通过

REM 运行打包脚本
echo.
echo 开始执行打包过程...
echo ============================================================
python simple_build.py

REM 检查是否成功生成exe文件
if exist "dist\台账数据提取工具.exe" (
    echo.
    echo ============================================================
    echo 🎉 打包成功完成！
    echo ============================================================
    echo 📁 可执行文件位置: dist\台账数据提取工具.exe
    echo 📊 文件大小: 
    for %%A in ("dist\台账数据提取工具.exe") do echo    %%~zA 字节
    echo.
    echo 💡 使用说明:
    echo    1. 可执行文件无需Python环境即可运行
    echo    2. 首次运行可能需要几秒钟启动时间
    echo    3. 可以复制到任何Windows电脑使用
    echo.
    echo ❓ 是否现在测试运行？ (Y/N)
    set /p choice=请选择: 
    if /i "%choice%"=="Y" (
        echo 启动测试...
        start "" "dist\台账数据提取工具.exe"
    )
) else (
    echo.
    echo ❌ 打包失败，未找到生成的exe文件
    echo 请检查上面的错误信息
)

echo.
pause
