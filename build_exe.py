# 用于构建可执行文件的脚本
import os
import subprocess
import sys

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        import PyInstaller
        print("PyInstaller已安装")
    except ImportError:
        print("正在安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("PyInstaller安装完成")

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 不显示控制台窗口
        "--name=台账数据提取工具",        # 可执行文件名称
        "--icon=icon.ico",              # 图标文件（如果有的话）
        "--add-data=config.py;.",       # 包含配置文件
        "data_extractor.py"             # 主程序文件
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists("icon.ico"):
        cmd.remove("--icon=icon.ico")
    
    try:
        subprocess.run(cmd, check=True)
        print("构建完成！可执行文件位于 dist/ 目录中")
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
    except FileNotFoundError:
        print("PyInstaller未找到，请确保已正确安装")

def main():
    """主函数"""
    print("=== 台账数据提取工具 - 可执行文件构建器 ===")
    
    # 检查必要文件
    required_files = ["data_extractor.py", "requirements.txt"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"缺少必要文件: {', '.join(missing_files)}")
        return
    
    # 安装依赖
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装完成")
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败: {e}")
        return
    
    # 安装PyInstaller
    install_pyinstaller()
    
    # 构建可执行文件
    build_executable()
    
    print("\n构建完成！")
    print("可执行文件位置: dist/台账数据提取工具.exe")
    print("您可以将此文件复制到任何Windows电脑上运行，无需安装Python环境。")

if __name__ == "__main__":
    main()
