# 用于构建可执行文件的脚本
import os
import subprocess
import sys

def install_dependencies():
    """安装所有必要的依赖包"""
    print("正在安装依赖包...")

    # 基础依赖包
    packages = [
        "pyautogui==0.9.54",
        "pyperclip==1.8.2",
        "pandas==2.2.1",
        "openpyxl==3.1.2",
        "pyinstaller"
    ]

    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package],
                                stdout=subprocess.DEVNULL, stderr=subprocess.STDOUT)
            print(f"✓ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {package} 安装失败: {e}")
            return False

    print("所有依赖包安装完成")
    return True

def build_executable():
    """构建可执行文件"""
    print("\n开始构建可执行文件...")

    # 清理之前的构建文件
    if os.path.exists("dist"):
        import shutil
        shutil.rmtree("dist")
        print("清理旧的构建文件")

    if os.path.exists("build"):
        import shutil
        shutil.rmtree("build")

    # PyInstaller命令参数
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 不显示控制台窗口
        "--name=台账数据提取工具",        # 可执行文件名称
        "--clean",                      # 清理临时文件
        "--noconfirm",                  # 不询问覆盖
        "data_extractor.py"             # 主程序文件
    ]

    try:
        print("执行PyInstaller打包...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            print("✓ 构建成功！")
            exe_path = os.path.join("dist", "台账数据提取工具.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"可执行文件: {exe_path}")
                print(f"文件大小: {file_size:.1f} MB")
                return True
            else:
                print("✗ 可执行文件未生成")
                return False
        else:
            print("✗ 构建失败")
            print("错误信息:")
            print(result.stderr)
            return False

    except Exception as e:
        print(f"构建过程出错: {e}")
        return False

def test_executable():
    """测试可执行文件"""
    exe_path = os.path.join("dist", "台账数据提取工具.exe")
    if os.path.exists(exe_path):
        print(f"\n可执行文件已生成: {exe_path}")
        print("您可以双击运行测试，或将其复制到其他电脑使用。")
        return True
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("    台账数据提取工具 - 可执行文件构建器")
    print("=" * 50)

    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        return False

    print(f"Python版本: {sys.version}")

    # 检查必要文件
    required_files = ["data_extractor.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
        print(f"错误: 缺少必要文件: {', '.join(missing_files)}")
        return False

    print("✓ 必要文件检查通过")

    # 安装依赖
    if not install_dependencies():
        print("依赖安装失败，无法继续构建")
        return False

    # 构建可执行文件
    if not build_executable():
        print("构建失败")
        return False

    # 测试可执行文件
    if test_executable():
        print("\n" + "=" * 50)
        print("构建完成！")
        print("=" * 50)
        print("可执行文件位置: dist/台账数据提取工具.exe")
        print("文件可以在任何Windows电脑上运行，无需Python环境。")
        return True

    return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n按回车键退出...")
            input()
        else:
            print("\n构建失败，按回车键退出...")
            input()
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"\n意外错误: {e}")
        input("按回车键退出...")
