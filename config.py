# 台账数据提取工具配置文件

# 默认配置参数
DEFAULT_CONFIG = {
    # 操作间隔时间（秒）
    'delay_between_actions': 0.5,
    
    # 复制后等待时间（秒）
    'wait_time_after_copy': 0.2,
    
    # 最大无变化尝试次数
    'max_unchanged_attempts': 3,
    
    # 默认输出路径
    'default_output_path': './output',
    
    # 启动倒计时时间（秒）
    'startup_countdown': 3,
    
    # 日志文件设置
    'log_to_file': False,
    'log_file_path': './logs/extraction.log',
    
    # Excel文件设置
    'excel_sheet_name': '台账数据',
    'include_timestamp_in_filename': True,
    
    # 安全设置
    'enable_failsafe': True,  # 启用PyAutoGUI的安全机制
    'failsafe_corner': True,  # 鼠标移到屏幕角落时停止
}

# 键盘快捷键配置
KEYBOARD_CONFIG = {
    'navigation_key': 'down',      # 导航键
    'copy_hotkey': ['ctrl', 'c'],  # 复制快捷键
    'paste_hotkey': ['ctrl', 'v'], # 粘贴快捷键
}

# UI配置
UI_CONFIG = {
    'window_title': '台账数据提取工具',
    'window_size': '600x500',
    'theme': 'default',
    'font_family': 'Arial',
    'font_size': 10,
}

# 数据处理配置
DATA_CONFIG = {
    'remove_empty_lines': True,
    'trim_whitespace': True,
    'remove_duplicates': False,
    'encoding': 'utf-8',
}
