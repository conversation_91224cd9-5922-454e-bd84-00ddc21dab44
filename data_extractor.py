import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pyautogui
import pyperclip
import pandas as pd
import time
import threading
from datetime import datetime
import os
import json

class DataExtractor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("台账数据提取工具")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 数据存储
        self.extracted_data = []
        self.is_running = False
        self.output_path = ""
        
        # 配置参数
        self.delay_between_actions = 0.5  # 操作间隔时间（秒）
        self.max_unchanged_attempts = 3   # 最大无变化尝试次数
        self.wait_time_after_copy = 0.2   # 复制后等待时间
        
        self.setup_ui()
        self.load_config()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="台账数据自动提取工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置参数", padding="10")
        config_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 操作间隔时间
        ttk.Label(config_frame, text="操作间隔时间(秒):").grid(row=0, column=0, sticky=tk.W)
        self.delay_var = tk.DoubleVar(value=self.delay_between_actions)
        delay_spinbox = ttk.Spinbox(config_frame, from_=0.1, to=5.0, increment=0.1, 
                                   textvariable=self.delay_var, width=10)
        delay_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 最大无变化尝试次数
        ttk.Label(config_frame, text="最大无变化尝试次数:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.max_attempts_var = tk.IntVar(value=self.max_unchanged_attempts)
        attempts_spinbox = ttk.Spinbox(config_frame, from_=1, to=10, increment=1,
                                      textvariable=self.max_attempts_var, width=10)
        attempts_spinbox.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))
        
        # 输出路径选择
        path_frame = ttk.Frame(main_frame)
        path_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(path_frame, text="输出路径:").grid(row=0, column=0, sticky=tk.W)
        self.path_var = tk.StringVar(value=os.getcwd())
        path_entry = ttk.Entry(path_frame, textvariable=self.path_var, width=40)
        path_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        
        browse_btn = ttk.Button(path_frame, text="浏览", command=self.browse_output_path)
        browse_btn.grid(row=0, column=2)
        
        path_frame.columnconfigure(1, weight=1)
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=2, pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="开始提取", 
                                   command=self.start_extraction, style="Accent.TButton")
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="停止提取", 
                                  command=self.stop_extraction, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.save_btn = ttk.Button(control_frame, text="保存数据", 
                                  command=self.save_data, state="disabled")
        self.save_btn.grid(row=0, column=2)
        
        # 进度显示区域
        progress_frame = ttk.LabelFrame(main_frame, text="提取进度", padding="10")
        progress_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.progress_var = tk.StringVar(value="等待开始...")
        progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.data_count_var = tk.StringVar(value="已提取数据: 0 条")
        count_label = ttk.Label(progress_frame, textvariable=self.data_count_var)
        count_label.grid(row=1, column=0, sticky=tk.W)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=8, width=70)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 使主窗口可调整大小
        main_frame.columnconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
    def browse_output_path(self):
        """浏览输出路径"""
        path = filedialog.askdirectory(initialdir=self.path_var.get())
        if path:
            self.path_var.set(path)
            
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def start_extraction(self):
        """开始数据提取"""
        if self.is_running:
            return
            
        # 更新配置
        self.delay_between_actions = self.delay_var.get()
        self.max_unchanged_attempts = self.max_attempts_var.get()
        self.output_path = self.path_var.get()
        
        # 保存配置
        self.save_config()
        
        # 重置数据
        self.extracted_data = []
        self.is_running = True
        
        # 更新UI状态
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.save_btn.config(state="disabled")
        
        self.log_message("开始数据提取...")
        self.log_message("请确保目标软件窗口处于活动状态")
        self.log_message(f"将在 3 秒后开始自动操作...")
        
        # 在新线程中执行提取操作
        self.extraction_thread = threading.Thread(target=self.extraction_worker)
        self.extraction_thread.daemon = True
        self.extraction_thread.start()
        
    def extraction_worker(self):
        """数据提取工作线程"""
        try:
            # 等待3秒让用户准备
            for i in range(3, 0, -1):
                if not self.is_running:
                    return
                self.root.after(0, lambda i=i: self.progress_var.set(f"倒计时: {i} 秒"))
                time.sleep(1)
            
            if not self.is_running:
                return
                
            self.root.after(0, lambda: self.progress_var.set("正在提取数据..."))
            
            unchanged_count = 0
            previous_data = ""
            row_count = 0
            
            while self.is_running and unchanged_count < self.max_unchanged_attempts:
                try:
                    # 按下↓键
                    pyautogui.press('down')
                    self.root.after(0, lambda: self.log_message("按下↓键"))
                    time.sleep(self.delay_between_actions)
                    
                    if not self.is_running:
                        break
                    
                    # 按下Ctrl+C复制数据
                    pyautogui.hotkey('ctrl', 'c')
                    self.root.after(0, lambda: self.log_message("执行Ctrl+C复制"))
                    time.sleep(self.wait_time_after_copy)
                    
                    if not self.is_running:
                        break
                    
                    # 获取剪贴板内容
                    try:
                        current_data = pyperclip.paste()
                    except Exception as e:
                        self.root.after(0, lambda: self.log_message(f"获取剪贴板数据失败: {str(e)}"))
                        continue
                    
                    # 检查数据是否有变化
                    if current_data == previous_data:
                        unchanged_count += 1
                        self.root.after(0, lambda: self.log_message(f"数据无变化 ({unchanged_count}/{self.max_unchanged_attempts})"))
                    else:
                        unchanged_count = 0
                        if current_data.strip():  # 只保存非空数据
                            self.extracted_data.append(current_data.strip())
                            row_count += 1
                            self.root.after(0, lambda: self.data_count_var.set(f"已提取数据: {row_count} 条"))
                            self.root.after(0, lambda: self.log_message(f"提取到新数据: {current_data[:50]}..."))
                        previous_data = current_data
                    
                    time.sleep(self.delay_between_actions)
                    
                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"操作出错: {str(e)}"))
                    break
            
            # 提取完成
            if self.is_running:
                self.root.after(0, lambda: self.log_message("数据提取完成"))
                self.root.after(0, lambda: self.progress_var.set("提取完成"))
            else:
                self.root.after(0, lambda: self.log_message("数据提取已停止"))
                self.root.after(0, lambda: self.progress_var.set("已停止"))
                
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"提取过程出错: {str(e)}"))
        finally:
            self.is_running = False
            # 更新UI状态
            self.root.after(0, self.extraction_finished)
            
    def extraction_finished(self):
        """提取完成后的UI更新"""
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        if self.extracted_data:
            self.save_btn.config(state="normal")
            
    def stop_extraction(self):
        """停止数据提取"""
        self.is_running = False
        self.log_message("正在停止提取...")
        
    def save_data(self):
        """保存提取的数据到Excel文件"""
        if not self.extracted_data:
            messagebox.showwarning("警告", "没有数据可保存")
            return
            
        try:
            # 创建DataFrame
            df = pd.DataFrame(self.extracted_data, columns=['提取数据'])
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'台账数据_{timestamp}.xlsx'
            filepath = os.path.join(self.output_path, filename)
            
            # 保存到Excel
            df.to_excel(filepath, index=False, engine='openpyxl')
            
            self.log_message(f"数据已保存到: {filepath}")
            messagebox.showinfo("成功", f"数据已保存到:\n{filepath}")
            
        except Exception as e:
            error_msg = f"保存数据失败: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
            
    def save_config(self):
        """保存配置到文件"""
        config = {
            'delay_between_actions': self.delay_between_actions,
            'max_unchanged_attempts': self.max_unchanged_attempts,
            'output_path': self.output_path
        }
        try:
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")
            
    def load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.delay_between_actions = config.get('delay_between_actions', 0.5)
                    self.max_unchanged_attempts = config.get('max_unchanged_attempts', 3)
                    self.output_path = config.get('output_path', os.getcwd())
                    
                    # 更新UI
                    self.delay_var.set(self.delay_between_actions)
                    self.max_attempts_var.set(self.max_unchanged_attempts)
                    self.path_var.set(self.output_path)
        except Exception as e:
            self.log_message(f"加载配置失败: {str(e)}")
            
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = DataExtractor()
    app.run()
