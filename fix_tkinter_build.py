#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复tkinter打包问题的脚本
"""

import os
import sys
import subprocess
import shutil

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        print(f"执行命令: {cmd}")
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True, encoding='utf-8')
        if result.stdout:
            print(f"输出: {result.stdout}")
        if result.stderr and result.returncode != 0:
            print(f"错误: {result.stderr}")
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        print(f"命令执行失败: {e}")
        return False, "", str(e)

def check_tkinter():
    """检查tkinter是否可用"""
    try:
        import tkinter
        import _tkinter
        print("✅ tkinter模块检查通过")
        return True
    except ImportError as e:
        print(f"❌ tkinter模块不可用: {e}")
        return False

def create_fixed_spec_file():
    """创建修复版本的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks.tcl_tk import tcltk_info

block_cipher = None

# 添加TCL/TK数据文件
datas = []
if tcltk_info.data_files:
    datas.extend(tcltk_info.data_files)

a = Analysis(
    ['data_extractor.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        '_tkinter',
        'pyautogui',
        'pyperclip',
        'pandas',
        'openpyxl',
        'threading',
        'json',
        'datetime',
        'os',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台账数据提取工具_修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('data_extractor_fixed.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 创建修复版spec文件成功")

def main():
    print("=" * 60)
    print("台账数据提取工具 - tkinter问题修复脚本")
    print("=" * 60)
    
    # 获取当前目录
    current_dir = os.getcwd()
    venv_dir = os.path.join(current_dir, "venv")
    venv_python = os.path.join(venv_dir, "Scripts", "python.exe")
    venv_pyinstaller = os.path.join(venv_dir, "Scripts", "pyinstaller.exe")
    
    print(f"当前目录: {current_dir}")
    print(f"虚拟环境目录: {venv_dir}")
    
    # 检查虚拟环境是否存在
    if not os.path.exists(venv_python):
        print("❌ 虚拟环境不存在，请先创建虚拟环境")
        return False
    
    print("✅ 虚拟环境已存在")
    
    # 在虚拟环境中检查tkinter
    print("\n1. 检查tkinter可用性...")
    success, output, error = run_command(f'"{venv_python}" -c "import tkinter; import _tkinter; print(\'tkinter可用\')"')
    if not success:
        print("❌ 虚拟环境中tkinter不可用")
        print("尝试重新安装Python或使用系统Python")
        return False
    
    print("✅ 虚拟环境中tkinter可用")
    
    # 清理旧的构建文件
    print("\n2. 清理旧的构建文件...")
    for dir_name in ['dist', 'build']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"删除 {dir_name} 目录")
    
    # 删除旧的spec文件
    for file in os.listdir('.'):
        if file.endswith('.spec'):
            os.remove(file)
            print(f"删除 {file}")
    
    # 创建修复版spec文件
    print("\n3. 创建修复版spec文件...")
    create_fixed_spec_file()
    
    # 方法1: 使用修复版spec文件打包
    print("\n4. 方法1: 使用修复版spec文件打包...")
    success, output, error = run_command(f'"{venv_pyinstaller}" data_extractor_fixed.spec')
    
    if success:
        exe_path = os.path.join("dist", "台账数据提取工具_修复版.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"✅ 方法1成功！")
            print(f"📁 可执行文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            return True
    
    print("❌ 方法1失败，尝试方法2...")
    
    # 方法2: 使用详细的命令行参数
    print("\n5. 方法2: 使用详细命令行参数...")
    
    # 清理
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    cmd = f'''"{venv_pyinstaller}" --onefile --windowed --name="台账数据提取工具_修复版2" --clean --noconfirm ^
        --hidden-import=tkinter ^
        --hidden-import=tkinter.ttk ^
        --hidden-import=tkinter.filedialog ^
        --hidden-import=tkinter.messagebox ^
        --hidden-import=_tkinter ^
        --hidden-import=pyautogui ^
        --hidden-import=pyperclip ^
        --hidden-import=pandas ^
        --hidden-import=openpyxl ^
        data_extractor.py'''
    
    success, output, error = run_command(cmd)
    
    if success:
        exe_path = os.path.join("dist", "台账数据提取工具_修复版2.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"✅ 方法2成功！")
            print(f"📁 可执行文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            return True
    
    print("❌ 方法2失败，尝试方法3...")
    
    # 方法3: 使用console模式（调试用）
    print("\n6. 方法3: 使用console模式（调试用）...")
    
    # 清理
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    cmd = f'"{venv_pyinstaller}" --onefile --console --name="台账数据提取工具_调试版" --clean data_extractor.py'
    
    success, output, error = run_command(cmd)
    
    if success:
        exe_path = os.path.join("dist", "台账数据提取工具_调试版.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"✅ 方法3成功！")
            print(f"📁 可执行文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            print("⚠️ 这是调试版本，会显示控制台窗口，但可以看到详细错误信息")
            return True
    
    print("❌ 所有方法都失败了")
    print("\n建议:")
    print("1. 检查Python安装是否完整")
    print("2. 尝试重新安装Python")
    print("3. 使用系统Python而不是虚拟环境")
    print("4. 检查是否有杀毒软件干扰")
    
    return False

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n{'✅ 成功' if success else '❌ 失败'}")
    except Exception as e:
        print(f"\n❌ 意外错误: {e}")
    finally:
        input("\n按回车键退出...")
