#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的打包脚本 - 台账数据提取工具
"""

import os
import sys
import subprocess
import time

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_package(package_name):
    """安装单个包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name, "--quiet"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装出错: {e}")
        return False

def install_dependencies():
    """安装依赖包"""
    print("\n=== 安装依赖包 ===")
    
    packages = [
        "pyautogui==0.9.54",
        "pyperclip==1.8.2", 
        "pandas==2.2.1",
        "openpyxl==3.1.2",
        "pyinstaller==6.3.0"
    ]
    
    failed_packages = []
    
    for package in packages:
        if not install_package(package):
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ 以下包安装失败: {', '.join(failed_packages)}")
        return False
    
    print("\n✅ 所有依赖包安装完成")
    return True

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['data_extractor.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'pyautogui',
        'pyperclip',
        'pandas',
        'openpyxl',
        'threading',
        'json'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台账数据提取工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('data_extractor.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 创建spec文件成功")

def build_executable():
    """构建可执行文件"""
    print("\n=== 开始构建可执行文件 ===")
    
    # 清理旧文件
    if os.path.exists('dist'):
        import shutil
        shutil.rmtree('dist')
        print("清理旧的dist目录")
    
    if os.path.exists('build'):
        import shutil
        shutil.rmtree('build')
        print("清理旧的build目录")
    
    # 创建spec文件
    create_spec_file()
    
    # 执行PyInstaller
    try:
        print("执行PyInstaller...")
        cmd = [sys.executable, "-m", "PyInstaller", "data_extractor.spec", "--clean"]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            
            # 检查生成的文件
            exe_path = os.path.join("dist", "台账数据提取工具.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"📁 可执行文件: {exe_path}")
                print(f"📊 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ 可执行文件未生成")
                return False
        else:
            print("❌ 构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 构建超时")
        return False
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("          台账数据提取工具 - 简化打包脚本")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return False
    
    # 检查主文件
    if not os.path.exists("data_extractor.py"):
        print("❌ 找不到主文件 data_extractor.py")
        input("按回车键退出...")
        return False
    
    print("✅ 主文件检查通过")
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        input("按回车键退出...")
        return False
    
    # 构建可执行文件
    if not build_executable():
        print("❌ 构建失败")
        input("按回车键退出...")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 打包完成！")
    print("=" * 60)
    print("📁 可执行文件位置: dist/台账数据提取工具.exe")
    print("💡 您可以将此文件复制到任何Windows电脑运行")
    print("⚠️  首次运行可能需要几秒钟启动时间")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n{'✅ 成功' if success else '❌ 失败'}")
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消操作")
    except Exception as e:
        print(f"\n❌ 意外错误: {e}")
    finally:
        input("\n按回车键退出...")
