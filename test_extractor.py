#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台账数据提取工具测试脚本
用于测试各个组件的功能
"""

import unittest
import os
import tempfile
import json
import pandas as pd
from unittest.mock import patch, MagicMock
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestDataExtractor(unittest.TestCase):
    """数据提取器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_save_load(self):
        """测试配置保存和加载"""
        # 模拟配置数据
        test_config = {
            'delay_between_actions': 1.0,
            'max_unchanged_attempts': 5,
            'output_path': self.temp_dir
        }
        
        # 保存配置
        config_file = os.path.join(self.temp_dir, 'test_config.json')
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        
        # 加载配置
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        self.assertEqual(test_config, loaded_config)
        print("✓ 配置保存和加载测试通过")
    
    def test_excel_export(self):
        """测试Excel导出功能"""
        # 模拟数据
        test_data = [
            "数据行1",
            "数据行2", 
            "数据行3"
        ]
        
        # 创建DataFrame
        df = pd.DataFrame(test_data, columns=['提取数据'])
        
        # 保存到Excel
        excel_file = os.path.join(self.temp_dir, 'test_data.xlsx')
        df.to_excel(excel_file, index=False, engine='openpyxl')
        
        # 验证文件存在
        self.assertTrue(os.path.exists(excel_file))
        
        # 读取并验证数据
        loaded_df = pd.read_excel(excel_file, engine='openpyxl')
        self.assertEqual(len(loaded_df), 3)
        self.assertEqual(loaded_df.iloc[0, 0], "数据行1")
        
        print("✓ Excel导出功能测试通过")
    
    @patch('pyperclip.paste')
    def test_clipboard_simulation(self, mock_paste):
        """测试剪贴板模拟"""
        # 模拟剪贴板数据变化
        clipboard_data = [
            "第一行数据",
            "第二行数据", 
            "第三行数据",
            "第三行数据",  # 重复数据，模拟到达末尾
            "第三行数据"
        ]
        
        mock_paste.side_effect = clipboard_data
        
        # 模拟数据提取逻辑
        extracted_data = []
        previous_data = ""
        unchanged_count = 0
        max_unchanged = 2
        
        for _ in range(len(clipboard_data)):
            current_data = mock_paste()
            
            if current_data == previous_data:
                unchanged_count += 1
                if unchanged_count >= max_unchanged:
                    break
            else:
                unchanged_count = 0
                if current_data.strip():
                    extracted_data.append(current_data.strip())
                previous_data = current_data
        
        # 验证结果
        expected_data = ["第一行数据", "第二行数据", "第三行数据"]
        self.assertEqual(extracted_data, expected_data)
        
        print("✓ 剪贴板模拟测试通过")
    
    def test_data_processing(self):
        """测试数据处理功能"""
        # 模拟原始数据（包含空行和重复数据）
        raw_data = [
            "  数据行1  ",
            "",
            "数据行2",
            "  数据行1  ",  # 重复数据
            "   ",
            "数据行3"
        ]
        
        # 数据清理
        cleaned_data = []
        seen_data = set()
        
        for item in raw_data:
            # 去除首尾空格
            cleaned_item = item.strip()
            
            # 跳过空行
            if not cleaned_item:
                continue
            
            # 去重（可选）
            if cleaned_item not in seen_data:
                cleaned_data.append(cleaned_item)
                seen_data.add(cleaned_item)
        
        # 验证结果
        expected_data = ["数据行1", "数据行2", "数据行3"]
        self.assertEqual(cleaned_data, expected_data)
        
        print("✓ 数据处理功能测试通过")

def run_dependency_check():
    """检查依赖包"""
    print("=== 依赖包检查 ===")
    
    required_packages = [
        'pyautogui',
        'pyperclip', 
        'pandas',
        'openpyxl',
        'tkinter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n所有依赖包已安装")
        return True

def run_system_check():
    """系统环境检查"""
    print("\n=== 系统环境检查 ===")
    
    # Python版本检查
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("✗ Python版本过低，建议使用3.7或更高版本")
        return False
    else:
        print("✓ Python版本符合要求")
    
    # 操作系统检查
    import platform
    os_name = platform.system()
    print(f"操作系统: {os_name}")
    
    if os_name == "Windows":
        print("✓ Windows系统，支持GUI自动化")
    else:
        print("⚠ 非Windows系统，可能需要额外配置")
    
    return True

def main():
    """主测试函数"""
    print("台账数据提取工具 - 功能测试")
    print("=" * 40)
    
    # 系统检查
    if not run_system_check():
        return False
    
    # 依赖检查
    if not run_dependency_check():
        return False
    
    # 功能测试
    print("\n=== 功能测试 ===")
    
    # 运行单元测试
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestDataExtractor)
    test_runner = unittest.TextTestRunner(verbosity=0)
    result = test_runner.run(test_suite)
    
    if result.wasSuccessful():
        print("\n✓ 所有测试通过")
        print("\n程序已准备就绪，可以正常使用！")
        return True
    else:
        print("\n✗ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 40)
    if success:
        print("测试完成 - 状态: 正常")
    else:
        print("测试完成 - 状态: 异常")
    
    input("\n按回车键退出...")
