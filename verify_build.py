#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证打包结果
"""

import os
import sys

def main():
    print("=" * 50)
    print("台账数据提取工具 - 打包结果验证")
    print("=" * 50)
    
    # 检查exe文件
    exe_path = os.path.join("dist", "台账数据提取工具.exe")
    
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path)
        file_size_mb = file_size / (1024 * 1024)
        
        print("🎉 打包成功！")
        print(f"📁 可执行文件位置: {exe_path}")
        print(f"📊 文件大小: {file_size_mb:.1f} MB ({file_size:,} 字节)")
        
        # 检查其他生成的文件
        if os.path.exists("build"):
            print("📂 构建目录: build/")
        
        if os.path.exists("台账数据提取工具.spec"):
            print("📄 规格文件: 台账数据提取工具.spec")
        
        print("\n✅ 打包完成总结:")
        print("1. 可执行文件已生成，无需Python环境即可运行")
        print("2. 可以将exe文件复制到任何Windows电脑使用")
        print("3. 首次运行可能需要几秒钟启动时间")
        print("4. 如果杀毒软件报警，请添加信任")
        
        print("\n📋 使用说明:")
        print("1. 双击运行 '台账数据提取工具.exe'")
        print("2. 配置操作参数（间隔时间、重试次数等）")
        print("3. 选择输出路径")
        print("4. 打开目标软件并定位到数据起始位置")
        print("5. 点击'开始提取'按钮")
        print("6. 等待自动提取完成")
        print("7. 点击'保存数据'将结果导出为Excel文件")
        
        return True
    else:
        print("❌ 打包失败")
        print(f"未找到可执行文件: {exe_path}")
        
        # 检查可能的问题
        if not os.path.exists("data_extractor.py"):
            print("❌ 源文件 data_extractor.py 不存在")
        
        if not os.path.exists("venv"):
            print("❌ 虚拟环境不存在")
        
        if not os.path.exists("venv/Scripts/pyinstaller.exe"):
            print("❌ PyInstaller 未安装在虚拟环境中")
        
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ 验证通过' if success else '❌ 验证失败'}")
    input("\n按回车键退出...")
