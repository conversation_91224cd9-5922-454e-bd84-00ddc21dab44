# 台账数据提取工具 - 使用说明

## 🎉 打包成功！

您的台账数据提取工具已经成功打包为可执行文件：

**📁 文件位置：** `dist/台账数据提取工具.exe`

## 📋 功能特点

- ✅ **无需Python环境** - 可在任何Windows电脑上直接运行
- ✅ **图形化界面** - 简洁易用的GUI操作界面
- ✅ **自动键盘控制** - 自动按下↓键和Ctrl+C进行数据提取
- ✅ **智能停止机制** - 检测数据变化，自动停止提取
- ✅ **Excel导出** - 将提取的数据保存为xlsx格式
- ✅ **参数配置** - 可自定义操作间隔、重试次数等
- ✅ **实时日志** - 显示操作进度和状态信息

## 🚀 使用步骤

### 1. 启动程序
- 双击运行 `台账数据提取工具.exe`
- 首次启动可能需要几秒钟时间

### 2. 配置参数
- **操作间隔时间**：设置每次操作之间的等待时间（建议0.5-2秒）
- **最大无变化尝试次数**：当数据不再变化时的最大尝试次数（建议3-5次）
- **输出路径**：选择Excel文件的保存位置

### 3. 准备目标软件
- 打开包含台账数据的exe软件
- 将光标定位到数据的起始位置
- 确保软件窗口处于活动状态
- 确认软件支持↓键导航和Ctrl+C复制

### 4. 开始提取
- 点击"开始提取"按钮
- 程序会倒计时3秒后开始自动操作
- 在日志区域查看实时提取进度
- 如需停止，点击"停止提取"按钮

### 5. 保存数据
- 提取完成后，点击"保存数据"按钮
- 数据将保存为Excel文件，文件名包含时间戳
- 格式：`台账数据_YYYYMMDD_HHMMSS.xlsx`

## ⚙️ 工作原理

1. **按下↓键** → 移动到下一行数据
2. **按下Ctrl+C** → 复制当前行数据到剪贴板
3. **检查数据变化** → 比较当前数据与上一次数据
4. **重复执行** → 如果数据有变化，继续步骤1-3
5. **自动停止** → 当连续几次数据无变化时自动停止
6. **保存数据** → 将所有提取的数据保存到Excel文件

## ⚠️ 注意事项

### 使用前准备
- ✅ 确保目标软件支持键盘导航（↓键）
- ✅ 确保目标软件支持复制操作（Ctrl+C）
- ✅ 建议先手动测试几次操作确认可行性
- ✅ 关闭其他可能干扰的程序

### 运行时注意
- ⚠️ 程序运行时请勿操作鼠标和键盘
- ⚠️ 如需紧急停止，可将鼠标移动到屏幕左上角
- ⚠️ 确保目标软件窗口始终保持活动状态
- ⚠️ 避免在提取过程中切换窗口

### 性能优化
- 🔧 根据软件响应速度调整操作间隔时间
- 🔧 对于大量数据，建议适当增加间隔时间
- 🔧 定期清理输出目录中的旧文件

## 🛠️ 故障排除

### 常见问题

**1. 程序无法启动**
- 检查是否被杀毒软件拦截，添加信任
- 确认Windows版本兼容性
- 尝试以管理员身份运行

**2. 键盘操作无效**
- 确认目标软件窗口处于活动状态
- 检查软件是否支持键盘快捷键
- 尝试增加操作间隔时间

**3. 数据提取不完整**
- 增加"最大无变化尝试次数"
- 检查目标软件的数据加载速度
- 适当增加操作间隔时间

**4. Excel文件保存失败**
- 检查输出路径是否存在且有写入权限
- 确认磁盘空间是否充足
- 检查文件名是否包含非法字符

### 错误代码说明
- 程序会在日志区域显示详细的错误信息
- 包括操作时间戳、具体操作和错误原因

## 📞 技术支持

如遇到问题，请提供以下信息：
1. Windows版本
2. 目标软件名称和版本
3. 具体的错误信息
4. 操作步骤描述

## 📝 更新日志

**v1.0.0** (当前版本)
- ✅ 基础数据提取功能
- ✅ GUI界面
- ✅ Excel导出
- ✅ 配置保存/加载
- ✅ 实时日志显示

## 📄 文件说明

- `台账数据提取工具.exe` - 主程序文件
- `config.json` - 配置文件（自动生成）
- `台账数据_*.xlsx` - 导出的数据文件

---

**🎯 提示：** 建议在正式使用前，先用少量数据进行测试，确认程序与目标软件的兼容性。
