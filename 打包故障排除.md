# 台账数据提取工具 - 打包故障排除指南

## 常见打包失败原因及解决方案

### 1. Python环境问题

#### 问题现象
- 提示"未检测到Python环境"
- Python版本过低

#### 解决方案
```bash
# 检查Python版本
python --version

# 如果版本低于3.7，请升级Python
# 下载地址: https://www.python.org/downloads/
```

### 2. 依赖包安装失败

#### 问题现象
- pip install 命令失败
- 网络连接超时
- 包版本冲突

#### 解决方案
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ pyautogui
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ pyperclip
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ pandas
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ openpyxl
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ pyinstaller

# 或者一次性安装
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements.txt
```

### 3. PyInstaller打包失败

#### 问题现象
- 打包过程中断
- 生成的exe文件无法运行
- 缺少模块错误

#### 解决方案

**方案1: 手动执行PyInstaller命令**
```bash
# 基础命令
pyinstaller --onefile --windowed --name="台账数据提取工具" data_extractor.py

# 如果有隐藏导入问题，使用详细命令
pyinstaller --onefile --windowed --name="台账数据提取工具" ^
    --hidden-import=tkinter ^
    --hidden-import=pyautogui ^
    --hidden-import=pyperclip ^
    --hidden-import=pandas ^
    --hidden-import=openpyxl ^
    data_extractor.py
```

**方案2: 使用spec文件**
```bash
# 生成spec文件
pyinstaller --onefile --windowed data_extractor.py

# 编辑生成的data_extractor.spec文件，然后执行
pyinstaller data_extractor.spec
```

### 4. 权限问题

#### 问题现象
- 访问被拒绝
- 无法创建文件

#### 解决方案
- 以管理员身份运行命令提示符
- 检查防病毒软件是否阻止
- 确保有足够的磁盘空间

### 5. 中文路径问题

#### 问题现象
- 路径包含中文字符导致错误

#### 解决方案
```bash
# 设置环境变量
set PYTHONIOENCODING=utf-8

# 或者将项目移动到英文路径下
```

### 6. PyAutoGUI特殊问题

#### 问题现象
- 打包后程序无法控制鼠标键盘
- 提示缺少依赖

#### 解决方案
```bash
# 安装额外依赖
pip install pillow
pip install opencv-python

# 在打包时添加数据文件
pyinstaller --add-data "C:\Python\Lib\site-packages\pyautogui;pyautogui" data_extractor.py
```

## 手动打包步骤

如果自动打包脚本失败，可以按以下步骤手动打包：

### 步骤1: 准备环境
```bash
# 1. 确认Python版本
python --version

# 2. 创建虚拟环境（可选但推荐）
python -m venv venv
venv\Scripts\activate

# 3. 安装依赖
pip install pyautogui pyperclip pandas openpyxl pyinstaller
```

### 步骤2: 测试程序
```bash
# 确保程序能正常运行
python data_extractor.py
```

### 步骤3: 执行打包
```bash
# 基础打包命令
pyinstaller --onefile --windowed --name="台账数据提取工具" data_extractor.py

# 如果失败，尝试添加更多参数
pyinstaller --onefile --windowed --name="台账数据提取工具" --clean --noconfirm data_extractor.py
```

### 步骤4: 测试exe文件
```bash
# 运行生成的exe文件
dist\台账数据提取工具.exe
```

## 替代方案

### 方案1: 使用auto-py-to-exe (图形界面)
```bash
pip install auto-py-to-exe
auto-py-to-exe
```

### 方案2: 使用cx_Freeze
```bash
pip install cx_Freeze
# 创建setup.py文件后执行
python setup.py build
```

### 方案3: 分发Python脚本
如果打包始终失败，可以：
1. 提供Python脚本 + requirements.txt
2. 提供安装说明
3. 用户自行安装Python环境运行

## 联系支持

如果以上方案都无法解决问题，请提供以下信息：
1. Python版本
2. 操作系统版本
3. 完整的错误信息
4. 执行的具体命令

## 常用命令速查

```bash
# 检查Python版本
python --version

# 检查已安装包
pip list

# 升级pip
python -m pip install --upgrade pip

# 清理pip缓存
pip cache purge

# 重新安装PyInstaller
pip uninstall pyinstaller
pip install pyinstaller

# 查看PyInstaller版本
pyinstaller --version
```
